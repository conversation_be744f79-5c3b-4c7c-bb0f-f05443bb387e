#%%
from common import test_network, get_klines, calculate_bollinger_bands, calculate_band_breakthrough,array
# 测试网络
test_network()

from binance.client import Client
import pandas as pd
# 设置显示选项
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
pd.set_option('display.width', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.max_colwidth', None)

# API配置
api_key = 'test'
api_secret = 'test'
client = Client(api_key, api_secret)
#%%
interval = '30m'
symbol = "BTCUSDC"

print('interval',interval)
print('symbol',symbol)

klines = get_klines(client, 1000, interval=interval, symbol=symbol)
# 输出开始时间和结束时间
print(f"开始时间: {klines['open_time'].min()}")
print(f"结束时间: {klines['open_time'].max()}")

klines_1m = get_klines(client, 30000, interval='1m', symbol=symbol)
# 输出开始时间和结束时间
print(f"开始时间: {klines_1m['open_time'].min()}")
print(f"结束时间: {klines_1m['open_time'].max()}")

# 增加boll轨道
df_with_bb = calculate_bollinger_bands(klines)
# 计算超出布林带的幅度
df = calculate_band_breakthrough(df_with_bb)
#%%
from common import order_price_calc

print('空单的挂单,按照最低价来计算')
order_price_calc(df,array,1.0)
#%%
print('多单的挂单,按照最高价来计算')
order_price_calc(df,array,-1.0)
#%%
from common import temp_test_price_calc
temp_test_price_calc(95176,array,-1.0)

#   94090.1 USDC

#%%

#%%
threshold = 0.65
direction_column_name = 'range'
order_column_name = 'open_time'

print(f"{direction_column_name} > {threshold} count: {df[df[direction_column_name] > threshold][direction_column_name].count()}")
df[df[direction_column_name] > threshold][['open_time', direction_column_name]].sort_values(by='range', ascending=False)
count = df[df[direction_column_name] > threshold][direction_column_name].count()
# 输出天数
print(f"天数: {(df['open_time'].max() - df['open_time'].min()).days}")
# 输出一天多少次
print(f"一天多少次: {count / (df['open_time'].max() - df['open_time'].min()).days:.1f}")
# 输出符合的记录
df[df[direction_column_name] > threshold][['open_time', direction_column_name]].sort_values(by=order_column_name, ascending=False)


# 按照日期分组,输出次数,按照时间排序
df['date'] = df['open_time'].dt.date  # 提取日期部分

# 筛选 + 分组统计 + 排序
result = (
    df[df[direction_column_name] > threshold]
    .groupby('date')
    .size()
    .reset_index(name='count')
    .sort_values(by='date',ascending=False)
)
print(result)

import matplotlib.pyplot as plt

plt.figure(figsize=(12, 6))
plt.plot(result['date'], result['count'], marker='o')
plt.xlabel('Date')
plt.ylabel('Count')
plt.title('Count per Day')
plt.xticks(rotation=45)
plt.tight_layout()
plt.grid(True)
plt.show()