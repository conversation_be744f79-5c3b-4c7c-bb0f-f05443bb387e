#!/usr/bin/env python3
"""
Async Binance Futures WebSocket Demo with Auto-Reconnect
======================================================

This script connects to Binance Futures WebSocket streams for BTCUSDT
30-minute K-line data, handles disconnections, and automatically reconnects.

Ping-Pong Protocol Implementation:
- Binance WebSocket server sends a ping message every 3 minutes
- When a ping is received, we immediately respond with a pong message with the same payload
- If Binance doesn't receive a pong response within 10 minutes, the connection will be closed
- Unprompted pong messages with empty payload are sent during periods of inactivity as a keepalive

Usage:
    python async_binance_websocket.py

Dependencies:
    - websockets
"""

import json
import asyncio
import websockets
import ssl
import logging

from bitcoin.kelvin.jupyter.common import send_message

# --- Configuration ---
WEBSOCKET_URLS = {
    "30m": "wss://fstream.binance.com/ws/btcusdc@kline_30m",
    "12h": "wss://fstream.binance.com/ws/btcusdc@kline_12h"
}
RECONNECT_DELAY = 0.5  # Seconds to wait before attempting to reconnect
RECEIVE_TIMEOUT = 60  # Seconds before assuming connection is dead if no message received
# Binance WebSocket server sends ping every 3 minutes
SERVER_PING_INTERVAL = 180  # 3 minutes in seconds
# Binance will disconnect if no pong received within 10 minutes
SERVER_PING_TIMEOUT = 600   # 10 minutes in seconds
CLOSE_TIMEOUT = 5     # Seconds to wait for graceful connection closure

# --- Global Variables for Price Tracking (Per Interval) ---
# Each interval maintains its own state independently
INTERVAL_STATE = {
    "30m": {
        "highest_price": None,
        "lowest_price": None,
        "start_time": None
    },
    "12h": {
        "highest_price": None,
        "lowest_price": None,
        "start_time": None
    }
}

# --- Logging Setup ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

async def connect_to_binance(interval_key, websocket_url):
    """
    Connects to the Binance WebSocket API, processes messages,
    and handles keep-alive pings.

    Args:
        interval_key (str): The interval identifier (e.g., '30m', '12h')
        websocket_url (str): The WebSocket URL to connect to
    """
    logging.info(f"[{interval_key}] Attempting to connect to {websocket_url}")
    try:
        # Create SSL context that doesn't verify certificates (Use with caution!)
        # In production, it's better to configure system certificates properly.
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        async with websockets.connect(
                websocket_url,
                ssl=ssl_context,
                ping_interval=None,  # Disable automatic ping, we'll handle Binance's ping-pong protocol manually
                close_timeout=CLOSE_TIMEOUT,
                # Increase connection timeout if needed
                # open_timeout=10
        ) as websocket:
            logging.info(f"[{interval_key}] Successfully connected to {websocket_url}")

            # Main loop to receive messages
            while True:
                try:
                    # Wait for a message with a timeout
                    message = await asyncio.wait_for(websocket.recv(), timeout=RECEIVE_TIMEOUT)

                    # Process the received message
                    try:
                        data = json.loads(message)

                        if 'e' in data and data['e'] == 'kline':
                            symbol = data['s']
                            kline = data['k']
                            start_time = kline['t']
                            interval = kline['i']
                            open_price = kline['o']
                            high_price = float(kline['h'])
                            low_price = float(kline['l'])
                            close_price = kline['c']
                            is_closed = kline['x']


                            # Update interval-specific highest and lowest prices
                            global INTERVAL_STATE

                            # Get current interval state
                            current_state = INTERVAL_STATE[interval_key]

                            if current_state["start_time"] is None:
                                current_state["start_time"] = start_time
                                logging.info(f"[{interval_key}] Initialized start time: {current_state['start_time']}")

                            # Initialize interval-specific variables if they're None
                            if current_state["highest_price"] is None:
                                current_state["highest_price"] = high_price
                                logging.info(f"[{interval_key}] Initialized highest price: {current_state['highest_price']}")
                            if current_state["lowest_price"] is None:
                                current_state["lowest_price"] = low_price
                                logging.info(f"[{interval_key}] Initialized lowest price: {current_state['lowest_price']}")

                            # Check if we have a new highest price for this interval
                            if high_price > current_state["highest_price"]:
                                previous_highest = current_state["highest_price"]
                                current_state["highest_price"] = high_price
                                #     输出当前振幅
                                range1 = (high_price - low_price) / low_price * 100
                                logging.info(f"[{interval_key}] Current range: {range1:.2f}% New highest price: {high_price} (previous: {previous_highest})")
                                # 创新高后，重新计算多单，先撤销所有的多单，然后按照新的价格挂多单
                                logging.info(f"[{interval_key}] 撤销所有的多单，然后按照新的价格挂多单")

                            # Check if we have a new lowest price for this interval
                            if low_price < current_state["lowest_price"]:
                                previous_lowest = current_state["lowest_price"]
                                current_state["lowest_price"] = low_price
                                #     输出当前振幅
                                range2 = (high_price - low_price) / low_price * 100
                                logging.info(f"[{interval_key}] Current range: {range2:.2f}% New lowest price: {low_price} (previous: {previous_lowest})")
                                logging.info(f"[{interval_key}] 撤销所有的空单，然后按照新的价格挂空单")

                            # Check if the start time has changed for this interval
                            if start_time != current_state["start_time"] or is_closed:
                                logging.info(f"[{interval_key}] Start time changed: {current_state['start_time']} -> {start_time}")
                                current_state["start_time"] = start_time

                                # Reset interval-specific price tracking
                                current_state["highest_price"] = None
                                current_state["lowest_price"] = None

                                # 检查当前的历史委托订单

                                # 使用更长的间隔时间发送消息，避免频率限制
                                # send_message(f"[{interval_key}] 新的K线周期开始,撤销没有成交的委托订单，并按照新的最高价和最低价重新挂单", min_interval=5)  # 5分钟间隔

                            # # Print the K-line data
                            # logging.info(
                            #     f"KLINE [{symbol}|{interval}] O:{open_price} H:{high_price} "
                            #     f"L:{low_price} C:{close_price} Closed:{is_closed}"
                            # )

                        elif 'ping' in data: # Handle Binance application-level pings
                            ping_payload = data['ping']
                            logging.info(f"[{interval_key}] Received application-level ping with payload: {ping_payload}")
                            # Respond immediately with the same payload
                            pong_data = json.dumps({"pong": ping_payload})
                            await websocket.send(pong_data)
                            logging.info(f"[{interval_key}] Sent pong response with payload: {ping_payload}")
                        else:
                            # Log other message types
                            logging.debug(f"Received other message: {json.dumps(data, indent=2)}")

                    # Handle errors during message processing
                    except json.JSONDecodeError:
                        logging.error(f"[{interval_key}] Failed to decode JSON: {message[:100]}...")
                    except KeyError as e:
                        logging.error(f"[{interval_key}] Missing key in data: {e}. Data: {data}")
                    except Exception as e:
                        logging.error(f"[{interval_key}] Error processing message: {e}. Message: {message[:100]}...")

                # Handle timeout waiting for message (connection might be dead)
                except asyncio.TimeoutError:
                    logging.warning(f"[{interval_key}] No message received for {RECEIVE_TIMEOUT} seconds. Connection might be stale.")
                    # Send an unprompted pong with empty payload as a keepalive
                    # This is allowed by Binance but not guaranteed to keep connection alive
                    try:
                        logging.info(f"[{interval_key}] Sending unprompted pong with empty payload as keepalive...")
                        pong_data = json.dumps({"pong": ""})
                        await websocket.send(pong_data)
                        logging.info(f"[{interval_key}] Unprompted pong sent successfully")

                        # Also try a protocol-level ping as backup
                        logging.info(f"[{interval_key}] Attempting protocol-level ping...")
                        pong_waiter = await websocket.ping()
                        await asyncio.wait_for(pong_waiter, timeout=10)
                        logging.info(f"[{interval_key}] Protocol-level pong received.")
                    except asyncio.TimeoutError:
                        logging.error(f"[{interval_key}] Ping failed - no pong received. Closing connection.")
                        break # Exit inner loop to trigger reconnect
                    except websockets.exceptions.ConnectionClosed:
                        logging.warning(f"[{interval_key}] Connection closed while sending ping/pong.")
                        break # Exit inner loop
                    # Continue the loop rather than breaking to give the connection a chance

                # Handle WebSocket connection closed exceptions
                except websockets.exceptions.ConnectionClosedOK:
                    logging.info(f"[{interval_key}] Connection closed normally (OK).")
                    break # Exit inner loop
                except websockets.exceptions.ConnectionClosedError as e:
                    logging.error(f"[{interval_key}] Connection closed with error: Code={e.code}, Reason={e.reason}")
                    break # Exit inner loop
                except websockets.exceptions.ConnectionClosed as e:
                    logging.warning(f"[{interval_key}] Connection closed unexpectedly: Code={e.code}, Reason={e.reason}")
                    break # Exit inner loop

                # Handle other WebSocket specific errors
                except websockets.exceptions.WebSocketException as e:
                    logging.error(f"[{interval_key}] WebSocket exception occurred: {e}")
                    break # Exit inner loop

                # Handle other potential errors in the loop
                except Exception as e:
                    logging.error(f"[{interval_key}] Unexpected error in receive loop: {e}", exc_info=True)
                    break # Exit inner loop

    # Handle exceptions during the initial connection attempt
    except websockets.exceptions.InvalidURI as e:
        logging.error(f"[{interval_key}] Invalid WebSocket URI: {e}")
    except websockets.exceptions.WebSocketException as e:
        logging.error(f"[{interval_key}] Failed to connect due to WebSocket exception: {e}")
    except OSError as e:
        logging.error(f"[{interval_key}] Failed to connect due to OS error (network issue?): {e}")
    except Exception as e:
        logging.error(f"[{interval_key}] Failed to connect due to unexpected error: {e}", exc_info=True)

    # Signal that the connection attempt (and processing) has ended
    logging.info(f"[{interval_key}] Connection process finished.")


async def connection_manager(interval_key, websocket_url):
    """Manages a single WebSocket connection with auto-reconnect."""
    while True:
        await connect_to_binance(interval_key, websocket_url)

        logging.info(f"[{interval_key}] Connection lost or failed. Retrying in {RECONNECT_DELAY} seconds...")
        # send_message("断线重连", min_interval=300)  # 5分钟间隔
        await asyncio.sleep(RECONNECT_DELAY)


async def main():
    """Main function that starts multiple WebSocket connections concurrently."""
    logging.info("Starting Async Binance Futures WebSocket Client")
    logging.info("==============================================")
    logging.info(f"Connecting to {len(WEBSOCKET_URLS)} streams: {list(WEBSOCKET_URLS.keys())}")

    # Create tasks for each WebSocket connection
    tasks = []
    for interval_key, websocket_url in WEBSOCKET_URLS.items():
        task = asyncio.create_task(
            connection_manager(interval_key, websocket_url),
            name=f"websocket-{interval_key}"
        )
        tasks.append(task)
        logging.info(f"Created task for {interval_key}: {websocket_url}")

    # Run all connections concurrently
    try:
        await asyncio.gather(*tasks)
    except Exception as e:
        logging.error(f"Error in main gather: {e}", exc_info=True)
        # Cancel all tasks if one fails
        for task in tasks:
            if not task.done():
                task.cancel()
        raise


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logging.info("\nKeyboardInterrupt received. Exiting...")
    except Exception as e:
        logging.critical(f"Critical error in main execution: {e}", exc_info=True)
